<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>案件处置分析</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      width: 300px;
      padding: 15px;
      background-color: #f5f5f5;
    }
    h1 {
      font-size: 18px;
      color: #333;
      text-align: center;
      margin-bottom: 20px;
    }
    .container {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }
    .btn-container {
      display: flex;
      justify-content: space-between;
      gap: 10px;
    }
    button {
      flex: 1;
      padding: 10px;
      border: none;
      border-radius: 4px;
      background-color: #4285f4;
      color: white;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    button:hover {
      background-color: #3367d6;
    }
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    #upload-container {
      border: 2px dashed #ccc;
      padding: 15px;
      text-align: center;
      background-color: white;
      border-radius: 4px;
    }
    #file-info {
      margin-top: 10px;
      font-size: 12px;
      color: #666;
    }
    .status {
      margin-top: 15px;
      padding: 10px;
      border-radius: 4px;
      font-size: 12px;
      display: none;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
    }
    .progress {
      background-color: #e2f3f7;
      color: #0c5460;
    }
  </style>
</head>
<body>
  <h1>案件处置分析</h1>
  <div class="container">
    <div id="upload-container">
      <input type="file" id="file-input" accept=".csv,.xlsx,.xls" style="display: none;">
      <button id="upload-btn">上传文件</button>
      <div id="file-info">支持格式：CSV, XLSX, XLS</div>
    </div>
    <div class="btn-container">
      <button id="analyze-btn" disabled>分析数据</button>
    </div>
    <div id="status-message" class="status"></div>
  </div>
  <script src="xlsx.full.min.js"></script>
  <script src="popup.js"></script>
</body>
</html> 