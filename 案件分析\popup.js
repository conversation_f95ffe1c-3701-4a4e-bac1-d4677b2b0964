document.addEventListener('DOMContentLoaded', function() {
  const fileInput = document.getElementById('file-input');
  const uploadBtn = document.getElementById('upload-btn');
  const analyzeBtn = document.getElementById('analyze-btn');
  const fileInfo = document.getElementById('file-info');
  const statusMessage = document.getElementById('status-message');
  
  let uploadedFile = null;
  let workbook = null;
  
  // 上传按钮点击事件
  uploadBtn.addEventListener('click', function() {
    fileInput.click();
  });
  
  // 文件选择事件
  fileInput.addEventListener('change', function(e) {
    if (e.target.files.length === 0) return;
    
    uploadedFile = e.target.files[0];
    const fileName = uploadedFile.name;
    const fileExt = fileName.split('.').pop().toLowerCase();
    
    if (['csv', 'xlsx', 'xls'].indexOf(fileExt) === -1) {
      showStatus('错误：请上传CSV、XLSX或XLS格式的文件', 'error');
      uploadedFile = null;
      fileInput.value = '';
      return;
    }
    
    fileInfo.textContent = `已选择：${fileName}`;
    analyzeBtn.disabled = false;
    showStatus('文件上传成功，点击"分析数据"按钮开始分析', 'success');
  });
  
  // 分析按钮点击事件
  analyzeBtn.addEventListener('click', function() {
    if (!uploadedFile) {
      showStatus('请先上传文件', 'error');
      return;
    }
    
    analyzeBtn.disabled = true;
    showStatus('正在分析数据，请稍候...', 'progress');
    
    const reader = new FileReader();
    
    reader.onload = function(e) {
      try {
        const data = new Uint8Array(e.target.result);
        workbook = XLSX.read(data, { type: 'array' });
        
        // 处理数据
        processData(workbook, uploadedFile.name);
      } catch (error) {
        console.error('处理文件时出错:', error);
        showStatus('处理文件时出错: ' + error.message, 'error');
        analyzeBtn.disabled = false;
      }
    };
    
    reader.onerror = function() {
      showStatus('读取文件时出错', 'error');
      analyzeBtn.disabled = false;
    };
    
    reader.readAsArrayBuffer(uploadedFile);
  });
  
  // 显示状态信息
  function showStatus(message, type) {
    statusMessage.textContent = message;
    statusMessage.className = 'status ' + type;
    statusMessage.style.display = 'block';
  }
  
  // 处理数据
  function processData(workbook, fileName) {
    // 获取第一个工作表
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    
    // 将工作表转换为JSON
    let jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    if (jsonData.length < 2) {
      showStatus('表格数据不足，请检查文件', 'error');
      analyzeBtn.disabled = false;
      return;
    }
    
    // 获取表头
    const headers = jsonData[0];
    
    // 查找必要的列索引
    const colIndexes = {
      '工单编号': headers.indexOf('工单编号'),
      '所属供电所': headers.indexOf('所属供电所'),
      '抢修班组': headers.indexOf('抢修班组'),
      '故障地址': headers.indexOf('故障地址'),
      '故障原因': headers.indexOf('故障原因'),
      '处理记录': headers.indexOf('处理记录'),
      '三级类别': headers.indexOf('三级类别')
    };
    
    // 检查必要的列是否存在
    const missingCols = Object.entries(colIndexes)
      .filter(([_, index]) => index === -1)
      .map(([col, _]) => col);
    
    if (missingCols.length > 0) {
      showStatus(`错误：表格缺少必要的列: ${missingCols.join(', ')}`, 'error');
      analyzeBtn.disabled = false;
      return;
    }
    
    try {
      // 数据行（不包括表头）
      const dataRows = jsonData.slice(1);
      
      // 1. 删除"故障原因"字段为"设备老化"、"客户设备问题"、"客户误报"和空白的数据
      let filteredData = dataRows.filter(row => {
        const faultReason = row[colIndexes['故障原因']];
        return faultReason && 
               faultReason !== '设备老化' && 
               faultReason !== '客户设备问题' && 
               faultReason !== '客户误报';
      });
      
      // 2. 删除"处理记录"字段为"跳闸"、"老化"、"脱落"、"掉落"、"预警不准确"和空白的数据
      filteredData = filteredData.filter(row => {
        const processRecord = row[colIndexes['处理记录']];
        return processRecord && 
               processRecord !== '跳闸' && 
               processRecord !== '老化' && 
               processRecord !== '脱落' && 
               processRecord !== '掉落' && 
               processRecord !== '预警不准确';
      });
      
      // 3. 删除("故障原因"字段为"过负荷")且("三级类别"字段为"表前线"、"连接线（含搭头线）"、"导线")的记录
      filteredData = filteredData.filter(row => {
        const faultReason = row[colIndexes['故障原因']];
        const thirdCategory = row[colIndexes['三级类别']];
        
        if (faultReason === '过负荷') {
          return !['表前线', '连接线（含搭头线）', '导线'].includes(thirdCategory);
        }
        return true;
      });
      
      // 4. 添加新列："案件分类"和"处置建议"
      const newHeaders = [...headers];
      if (!newHeaders.includes('案件分类')) {
        newHeaders.push('案件分类');
        colIndexes['案件分类'] = newHeaders.length - 1;
      }
      if (!newHeaders.includes('处置建议')) {
        newHeaders.push('处置建议');
        colIndexes['处置建议'] = newHeaders.length - 1;
      }
      
      // 5 & 6. 填写"案件分类"和"处置建议"字段
      const processedData = filteredData.map(row => {
        const newRow = [...row];
        while (newRow.length < newHeaders.length) {
          newRow.push('');
        }
        
        const faultReason = row[colIndexes['故障原因']];
        const processRecord = row[colIndexes['处理记录']] || '';
        
        // 5. 填写"案件分类"字段
        let caseCategory = '人工判断案件';
        if (faultReason === '过负荷') {
          caseCategory = '疑似案件';
        } else if (processRecord.includes('烧')) {
          caseCategory = '疑似案件';
        }
        newRow[colIndexes['案件分类']] = caseCategory;
        
        // 6. 填写"处置建议"字段
        let suggestion = '';
        if (caseCategory === '疑似案件') {
          suggestion = '该案件可能符合"财产一切险"或"机器损坏险"理赔范围，请核实后尽快通过供服系统报案';
        } else if (faultReason === '车辆' || faultReason === '建设施工') {
          suggestion = '请核实电力资产是否受损，如无法联系对方赔偿，可通过供服系统报案';
        } else {
          suggestion = '请核实电力资产是否受损，如有受损，请尽快通过供服系统报案';
        }
        newRow[colIndexes['处置建议']] = suggestion;
        
        return newRow;
      });
      
      // 7. 保留指定字段
      const keyCols = ['工单编号', '所属供电所', '抢修班组', '故障地址', '故障原因', '处理记录', '案件分类', '处置建议'];
      const keyColIndexes = keyCols.map(col => newHeaders.indexOf(col)).filter(idx => idx !== -1);
      
      const finalHeaders = keyCols.filter(col => newHeaders.includes(col));
      const finalData = processedData.map(row => keyColIndexes.map(idx => row[idx]));
      
      // 创建新的工作簿
      const newWorkbook = XLSX.utils.book_new();
      const newWorksheet = XLSX.utils.aoa_to_sheet([finalHeaders, ...finalData]);
      XLSX.utils.book_append_sheet(newWorkbook, newWorksheet, '分析结果');
      
      // 8. 下载处理后的文件
      const fileNameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
      const outputFileName = `${fileNameWithoutExt}_分析后.xlsx`;
      
      XLSX.writeFile(newWorkbook, outputFileName);
      
      showStatus(`分析完成！已下载文件: ${outputFileName}`, 'success');
      analyzeBtn.disabled = false;
      
    } catch (error) {
      console.error('处理数据时出错:', error);
      showStatus('处理数据时出错: ' + error.message, 'error');
      analyzeBtn.disabled = false;
    }
  }
}); 