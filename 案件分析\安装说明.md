# 案件处置分析 Chrome插件

这是一个Chrome浏览器插件，用于分析案件数据并提供处置建议。

## 功能

- 支持上传CSV、XLSX、XLS格式的表格文件
- 根据规则自动筛选和分析数据
- 自动添加"案件分类"和"处置建议"字段
- 下载分析后的表格

## 数据处理规则

1. 删除"故障原因"字段为"设备老化"、"客户设备问题"、"客户误报"和空白的数据
2. 删除"处理记录"字段为"跳闸"、"老化"、"脱落"、"掉落"、"预警不准确"和空白的数据
3. 删除("故障原因"字段为"过负荷")且("三级类别"字段为"表前线"、"连接线（含搭头线）"、"导线")的记录
4. 为剩余数据添加"案件分类"和"处置建议"字段
5. 根据规则填写"案件分类"字段
6. 根据规则填写"处置建议"字段
7. 保留指定字段
8. 下载分析后的表格

## 安装方法

1. 下载本插件的ZIP文件并解压
2. 打开Chrome浏览器，进入扩展程序页面（chrome://extensions/）
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择解压后的文件夹

## 使用方法

1. 点击Chrome工具栏中的插件图标
2. 点击"上传文件"按钮选择要分析的表格文件
3. 点击"分析数据"按钮开始分析
4. 分析完成后，会自动下载处理后的表格文件

## 注意事项

- 上传的表格文件第一行必须是字段名
- 表格必须包含"工单编号"、"所属供电所"、"抢修班组"、"故障地址"、"故障原因"、"处理记录"、"三级类别"等字段
- 分析后的文件命名格式为：原文件名_分析后.xlsx 